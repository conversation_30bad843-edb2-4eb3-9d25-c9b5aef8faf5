package database

import (
	"log"

	"linguan/internal/config"

	_ "github.com/mattn/go-sqlite3" // 确保 SQLite 驱动被导入
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// NewDB 初始化数据库连接
func NewDB(cfg *config.Config) *gorm.DB {
	var db *gorm.DB
	var err error

	switch cfg.Database.Type {
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(cfg.Database.DSN), &gorm.Config{})
	default:
		log.Fatalf("不支持的数据库类型: %s", cfg.Database.Type)
	}

	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	return db
}
