# GoChen 架构设计文档

## 概述

GoChen 是一个基于 Go 语言的轻量级 Web 开发框架，采用领域驱动设计（DDD）原则，支持三种渐进式的业务模型：极简模型、CQRS 模型和 Event Sourcing 模型。

## 架构原则

### 1. 清洁架构
- **依赖倒置**: 高层模块不依赖低层模块，都依赖于抽象
- **单一职责**: 每个模块只负责一个职责
- **开闭原则**: 对扩展开放，对修改关闭

### 2. 领域驱动设计
- **聚合根**: 保证业务一致性的边界
- **实体和值对象**: 明确区分有身份的对象和无身份的对象
- **领域服务**: 处理跨聚合的业务逻辑

### 3. 事件驱动架构
- **事件溯源**: 通过事件重建聚合状态
- **CQRS**: 命令查询分离，优化读写性能
- **最终一致性**: 通过事件实现分布式系统的一致性

## 分层架构

```
┌─────────────────────────────────────────────────────────┐
│                    应用层 (app/)                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐   │
│  │  通用API     │  │    CQRS     │  │ Event Sourcing  │   │
│  │   注册器     │  │   应用服务   │  │    应用服务      │   │
│  └─────────────┘  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│                    领域层 (domain/)                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐   │
│  │    实体      │  │   聚合根     │  │  事件聚合根      │   │
│  └─────────────┘  └─────────────┘  └─────────────────┘   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐   │
│  │  仓库接口    │  │   领域服务   │  │   事件仓库      │   │
│  └─────────────┘  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│                   基础设施层                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐   │
│  │   数据库     │  │   事件存储   │  │    事件总线     │   │
│  └─────────────┘  └─────────────┘  └─────────────────┘   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐   │
│  │    缓存      │  │   快照存储   │  │     投影        │   │
│  └─────────────┘  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

## 三种业务模型

### 1. 极简模型 (Simple Model)

**适用场景**: 简单的 CRUD 操作，快速原型开发

**核心组件**:
- `domain/model/entity.go` - 基础实体
- `domain/service/service.go` - 通用服务
- `domain/repo/repo.go` - 仓库接口
- `app/app.go` - API 注册器

**特点**:
- 开发速度快
- 学习成本低
- 适合简单业务场景
- 支持完整的 CRUD 操作和批量操作

**使用示例**:
```go
type User struct {
    model.Entity
    Name  string `json:"name"`
    Email string `json:"email"`
}

userService := service.NewService("user", userRepo)
app.RegisterApi(userService, router.Group("/users"))
```

### 2. CQRS 模型 (Command Query Responsibility Segregation)

**适用场景**: 中等复杂度业务，需要命令查询分离

**核心组件**:
- `domain/model/aggregate/aggregate.go` - 聚合根
- `app/cqrs/cmd.go` - 命令总线
- `app/cqrs/query.go` - 查询总线
- `app/cqrs/handler.go` - 处理器管理
- `app/cqrs/middleware.go` - 中间件

**特点**:
- 命令查询分离
- 支持复杂业务规则
- 中间件支持
- 并发控制

**使用示例**:
```go
type CreateUserCommand struct {
    cqrs.BaseCommand
    Name  string `json:"name"`
    Email string `json:"email"`
}

type CreateUserHandler struct{}

func (h *CreateUserHandler) Handle(ctx context.Context, cmd CreateUserCommand) error {
    // 处理创建用户命令
    return nil
}

commandBus := cqrs.NewCommandBus()
cqrs.RegisterHandlerFor(commandBus, &CreateUserHandler{})
```

### 3. Event Sourcing 模型

**适用场景**: 复杂业务场景，需要事件溯源和完整审计

**核心组件**:
- `event/eventstore.go` - 事件存储
- `event/eventbus.go` - 事件总线
- `event/projection.go` - 事件投影
- `event/snapshot.go` - 快照机制
- `domain/model/eventaggregate/` - 事件聚合根

**特点**:
- 完整的事件历史
- 时间旅行调试
- 审计日志
- 最终一致性
- 高可扩展性

**使用示例**:
```go
type Order struct {
    eventaggregate.EventSourcedAggregate
    CustomerID int64   `json:"customer_id"`
    Amount     float64 `json:"amount"`
    Status     string  `json:"status"`
}

func (o *Order) CreateOrder(customerID int64, amount float64) error {
    return o.RaiseEvent("OrderCreated", map[string]interface{}{
        "customer_id": customerID,
        "amount":      amount,
    })
}
```

## 核心组件详解

### 1. 事件存储 (EventStore)

事件存储是 Event Sourcing 的核心，负责持久化和检索事件。

**接口定义**:
```go
type EventStore interface {
    SaveEvents(ctx context.Context, aggregateID int64, events []Event, expectedVersion uint64) error
    GetEvents(ctx context.Context, aggregateID int64, fromVersion uint64) ([]Event, error)
    GetAllEvents(ctx context.Context, aggregateID int64) ([]Event, error)
    GetEventStream(ctx context.Context, fromTimestamp time.Time) ([]Event, error)
    AggregateExists(ctx context.Context, aggregateID int64) (bool, error)
    GetAggregateVersion(ctx context.Context, aggregateID int64) (uint64, error)
}
```

**特性**:
- 版本冲突检测
- 事件序列化/反序列化
- 并发安全
- 内存和数据库实现

### 2. 事件总线 (EventBus)

事件总线负责事件的发布和订阅，支持异步处理。

**接口定义**:
```go
type EventBus interface {
    Publish(ctx context.Context, events ...Event) error
    Subscribe(eventType string, handler EventHandler) error
    Unsubscribe(eventType string, handler EventHandler) error
    Start(ctx context.Context) error
    Stop() error
}
```

**特性**:
- 多工作协程并发处理
- 事件处理失败重试
- 处理器生命周期管理
- 统计和监控

### 3. 投影管理器 (ProjectionManager)

投影管理器负责管理读模型的生成和维护。

**特性**:
- 投影注册和发现
- 投影状态管理
- 投影重建
- 健康监控

### 4. 快照机制 (SnapshotManager)

快照机制用于优化聚合重建性能。

**特性**:
- 多种快照策略
- 快照存储和恢复
- 快照清理
- 性能优化

## 中间件系统

GoChen 提供了丰富的中间件支持：

### 1. 日志中间件
记录请求处理的详细信息，包括执行时间和错误信息。

### 2. 验证中间件
自动验证请求数据的有效性。

### 3. 授权中间件
支持自定义授权逻辑。

### 4. 缓存中间件
为查询请求提供缓存支持。

### 5. 重试中间件
支持失败重试机制。

### 6. 恢复中间件
处理 panic 并记录错误信息。

### 7. 计时中间件
记录请求处理时间。

### 8. 指标中间件
收集性能指标。

## 性能优化

### 1. 批量操作
支持批量创建、更新和删除操作，减少数据库交互次数。

### 2. 分页查询
支持高效的分页查询，避免大量数据传输。

### 3. 快照机制
通过快照减少事件回放次数，提高聚合重建性能。

### 4. 异步处理
事件处理采用异步模式，提高系统吞吐量。

### 5. 并发控制
使用乐观锁控制并发访问，避免数据竞争。

## 扩展性设计

### 1. 插件化架构
通过接口抽象，支持不同的存储实现。

### 2. 中间件管道
支持自定义中间件，扩展框架功能。

### 3. 事件处理器
支持动态注册事件处理器。

### 4. 投影系统
支持多种读模型生成策略。

## 最佳实践

### 1. 聚合设计
- 保持聚合边界清晰
- 避免过大的聚合
- 使用事件进行聚合间通信

### 2. 事件设计
- 事件应该是过去时态
- 包含足够的上下文信息
- 保持事件的不可变性

### 3. 投影设计
- 根据查询需求设计投影
- 保持投影的幂等性
- 支持投影重建

### 4. 性能优化
- 合理使用快照
- 优化事件存储
- 监控系统性能

## 监控和调试

### 1. 健康检查
提供系统健康状态检查接口。

### 2. 统计信息
收集各组件的运行统计信息。

### 3. 日志记录
详细记录系统运行日志。

### 4. 错误处理
统一的错误处理和恢复机制。
